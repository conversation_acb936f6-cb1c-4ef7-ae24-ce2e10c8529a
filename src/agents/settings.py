import random
from datetime import datetime
from zoneinfo import ZoneInfo

from agents.agent_conf import AgentInfo
from agents.language_settings import LanguageSettings
from config import load_config


class AgentSettings:
    languages = {}

    def __init__(self, agent_info: AgentInfo, profile_prompt=None, languages=None):
        # Load configuration from YAML files
        self._prompts_config = load_config('prompts')
        self._messages_config = load_config('messages')

        self.config = agent_info
        self.voice = self.config.voice
        self.profile_prompt = profile_prompt
        self.lang_settings = LanguageSettings(languages)
        self.tools_prompt = self._build_tools_prompt()
        self.default_prompt = """"""

    @property
    def response_style(self) -> str:
        """Get response style from YAML configuration."""
        return self._prompts_config.get('response_style', '')

    @property
    def silence_phrases(self) -> dict:
        """Get silence phrases from YAML configuration."""
        return self._messages_config.get('silence_phrases', {})

    def _build_tools_prompt(self) -> str:
        """Build tools prompt from YAML configuration."""
        base_prompt = self._prompts_config.get('tools_prompt_base', '')
        function_prompts = self._prompts_config.get('function_prompts', {})

        prompt_parts = [base_prompt]
        for function in self.config.functions:
            if function in function_prompts:
                prompt_parts.append(function_prompts[function])

        return "\n".join(prompt_parts)

    def get_time_update(self, timezone):
        """Get formatted time update string."""
        now = datetime.now(ZoneInfo(timezone))
        weekday = now.weekday()
        time_format = self._messages_config.get('time_format',
                                                "Current time: {current_time}  Weekday: {weekday} Time zone: {timezone}")
        return time_format.format(current_time=now, weekday=weekday, timezone=timezone)

    def get_silence_message(self, language=None) -> str:
        """Get a random silence message for the specified language."""
        locale = self.lang_settings.get_locale(language)
        phrases = self.silence_phrases.get(locale, self.silence_phrases.get('en-US', []))
        return random.choice(phrases) if phrases else "Are you still there?"

    def get_system_prompt(self):
        """Build the complete system prompt."""
        language_instructions = self.lang_settings.get_language_instructions()
        return f'{self.profile_prompt} {self.config.mission.get_prompt(self.default_prompt)} {self.tools_prompt} {self.response_style} {language_instructions}'
