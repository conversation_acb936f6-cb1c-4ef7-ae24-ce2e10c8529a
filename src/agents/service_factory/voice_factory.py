import json
import os
from typing import Optional

from livekit.plugins import elevenlabs

from agents.agent_conf import AgentConfig
from agents.agent_instructions import AgentInstructions
from app.config import get_config

config = get_config()


def get_google_credentials():
    """Get Google credentials from the configured path."""
    credentials_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                                    'speech-api-credentials.json')
    try:
        return credentials_path
    except FileNotFoundError:
        raise ValueError(f"Google Speech API credentials file not found at {credentials_path}")
    except json.JSONDecodeError:
        raise ValueError(f"Invalid JSON in Google Speech API credentials file at {credentials_path}")


def create_tts(provider: str = "11labs", agent_settings: Optional[AgentConfig] = None):
    voice_info = agent_settings.voice
    voice_settings = voice_info
    if provider.lower() == "playai":
        from livekit.plugins import playai
        return playai.TTS(
            voice=voice_info.voiceId,
            model=voice_info.model
        )

    elif provider.lower() == "11labs":
        if not agent_settings:
            raise ValueError("agent_settings is required for ElevenLabs TTS")

        return elevenlabs.TTS(
            api_key=config.elevenlabs.api_key,
            voice_id=voice_info.voiceId,
            voice_settings=elevenlabs.tts.VoiceSettings(
                voice_settings.stability,
                voice_settings.similarity_boost,
                voice_settings.style,
                voice_settings.use_speaker_boost
            ),
            streaming_latency=voice_settings.streaming_latency
        )

    elif provider.lower() == "google":
        try:
            credentials = get_google_credentials()
            google_languages = {
                'eng': 'en-US',
                'ara': 'ar-XA',
                'ukr': 'uk-UA',
                'th': 'th-TH'
            }

            language = 'en-US'
            if agent_settings and agent_settings.languages in google_languages:
                language = google_languages[agent_settings.languages]
            from livekit.plugins import google
            return google.TTS(
                language=language,
                gender="neutral",
                encoding="linear16",
                sample_rate=16000,
                speaking_rate=1.0,
                credentials_file=credentials
            )
        except ValueError as e:
            raise ValueError(f"Failed to initialize Google TTS: {str(e)}")

    else:
        raise ValueError(f"Unsupported TTS provider: {provider}")
